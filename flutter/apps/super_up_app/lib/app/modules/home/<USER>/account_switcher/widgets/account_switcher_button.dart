// Copyright 2023, the hatemragab project author.
// All rights reserved. Use of this source code is governed by a
// MIT license that can be found in the LICENSE file.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:super_up_core/super_up_core.dart';
import 'package:s_translation/generated/l10n.dart';
import '../views/account_switcher_modal.dart';
import '../../../../auth/login/views/login_view.dart';
import '../../../mobile/settings_tab/widgets/settings_list_item_tile.dart';

class AccountSwitcherButton extends StatelessWidget {
  const AccountSwitcherButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: _checkMultipleAccounts(),
      builder: (context, snapshot) {
        final hasMultipleAccounts = snapshot.data ?? false;

        return SettingsListItemTile(
          color: Colors.purple,
          title: hasMultipleAccounts
              ? S.of(context).switchAccount
              : S.of(context).addAccount,
          subtitle: hasMultipleAccounts
              ? S.of(context).manageYourAccounts.text
              : S.of(context).addAnotherAccount.text,
          icon: hasMultipleAccounts
              ? CupertinoIcons.person_2_fill
              : CupertinoIcons.person_add_solid,
          onTap: () => _handleTap(context, hasMultipleAccounts),
        );
      },
    );
  }

  Future<bool> _checkMultipleAccounts() async {
    await MultiAccountManager.instance.initialize();
    return MultiAccountManager.instance.hasMultipleAccounts;
  }

  void _handleTap(BuildContext context, bool hasMultipleAccounts) {
    if (hasMultipleAccounts) {
      _showAccountSwitcher(context);
    } else {
      _navigateToAddAccount(context);
    }
  }

  void _showAccountSwitcher(BuildContext context) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => const AccountSwitcherModal(),
    );
  }

  void _navigateToAddAccount(BuildContext context) {
    context.toPage(const LoginView(showBackButton: true));
  }
}
